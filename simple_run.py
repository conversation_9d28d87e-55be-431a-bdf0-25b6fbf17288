#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط لبرنامج إدارة الأموال
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🏦 برنامج إدارة الأموال الشخصية")
print("=" * 50)

try:
    print("🔍 فحص المكتبات...")
    
    # فحص customtkinter
    try:
        import customtkinter
        print("✅ customtkinter متوفر")
    except ImportError as e:
        print(f"❌ customtkinter غير متوفر: {e}")
        sys.exit(1)
    
    # فحص bcrypt
    try:
        import bcrypt
        print("✅ bcrypt متوفر")
    except ImportError as e:
        print(f"❌ bcrypt غير متوفر: {e}")
        sys.exit(1)
    
    # فحص PIL
    try:
        from PIL import Image
        print("✅ Pillow متوفر")
    except ImportError as e:
        print(f"❌ Pillow غير متوفر: {e}")
        sys.exit(1)
    
    print("\n🚀 بدء تشغيل البرنامج...")
    
    # استيراد واجهة تسجيل الدخول
    from gui.login_window import LoginWindow
    print("✅ تم استيراد واجهة تسجيل الدخول")
    
    # إنشاء وتشغيل التطبيق
    app = LoginWindow()
    print("✅ تم إنشاء التطبيق")
    
    print("🎯 تشغيل الواجهة الرسومية...")
    app.run()
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت جميع المتطلبات")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

print("\n👋 انتهى البرنامج")
