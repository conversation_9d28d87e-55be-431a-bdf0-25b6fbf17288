#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة الأموال الشخصية
نظام شامل لإدارة الواردات والمصروفات والحسابات المالية

المطور: mohdam
التاريخ: 2025
"""

import sys
import os
import logging
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# إعداد نظام السجلات
def setup_logging():
    """إعداد نظام تسجيل الأحداث"""
    import tempfile
    
    # محاولة استخدام مجلد المستخدم أولاً
    logs_dir = None
    log_filename = None
    
    try:
        # محاولة 1: مجلد المستخدم
        user_home = os.path.expanduser("~")
        app_data_dir = os.path.join(user_home, "AppData", "Local", "MoneyManager")
        logs_dir = os.path.join(app_data_dir, "logs")
        os.makedirs(logs_dir, exist_ok=True)
        log_filename = os.path.join(logs_dir, f"money_manager_{datetime.now().strftime('%Y%m%d')}.log")
        print(f"✅ تم إنشاء مجلد السجلات: {logs_dir}")
    except (PermissionError, OSError) as e:
        print(f"⚠️ لا يمكن إنشاء مجلد السجلات في مجلد المستخدم: {e}")
        try:
            # محاولة 2: مجلد temp
            logs_dir = tempfile.gettempdir()
            log_filename = os.path.join(logs_dir, f"money_manager_{datetime.now().strftime('%Y%m%d')}.log")
            print(f"✅ استخدام مجلد temp للسجلات: {logs_dir}")
        except Exception as e2:
            print(f"⚠️ لا يمكن إنشاء ملف السجل: {e2}")
            log_filename = None

    # تكوين نظام السجلات
    handlers = [logging.StreamHandler(sys.stdout)]  # إضافة console handler دائماً
    
    if log_filename:
        try:
            handlers.append(logging.FileHandler(log_filename, encoding='utf-8'))
        except Exception as e:
            print(f"⚠️ لا يمكن إنشاء ملف السجل: {e}")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=handlers
    )

    logging.info("تم بدء تشغيل برنامج إدارة الأموال")

def check_dependencies():
    """فحص المتطلبات المطلوبة"""
    required_modules = [
        'customtkinter',
        'mysql.connector',
        'bcrypt',
        'PIL'
    ]

    missing_modules = []

    for module in required_modules:
        try:
            if module == 'mysql.connector':
                import mysql.connector
            elif module == 'PIL':
                from PIL import Image
            else:
                __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        print("❌ المتطلبات التالية غير مثبتة:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n💡 لتثبيت المتطلبات، قم بتشغيل:")
        print("   pip install -r requirements.txt")
        return False

    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    # استخدام مجلد المستخدم لتجنب مشاكل الصلاحيات
    user_home = os.path.expanduser("~")
    app_data_dir = os.path.join(user_home, "AppData", "Local", "MoneyManager")
    
    directories = [
        'logs',
        'backups',
        'uploads',
        'reports'
    ]

    for directory in directories:
        full_path = os.path.join(app_data_dir, directory)
        os.makedirs(full_path, exist_ok=True)
        logging.info(f"تم إنشاء/التحقق من المجلد: {full_path}")
    
    # مجلد assets يبقى في مجلد التطبيق (للقراءة فقط)
    assets_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'assets')
    if os.path.exists(assets_dir):
        logging.info(f"تم التحقق من مجلد: {assets_dir}")

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # إعداد نظام السجلات
        setup_logging()

        # فحص المتطلبات
        if not check_dependencies():
            # تجنب استخدام input() في الملف التنفيذي
            import time
            print("❌ المتطلبات غير مكتملة. سيتم إغلاق البرنامج خلال 5 ثوان...")
            time.sleep(5)
            return

        # إنشاء المجلدات المطلوبة
        create_directories()

        # استيراد وتشغيل نافذة تسجيل الدخول
        from gui.login_window import LoginWindow

        logging.info("بدء تشغيل واجهة المستخدم")

        # إنشاء وتشغيل التطبيق
        try:
            app = LoginWindow()
            app.run()
            logging.info("تم إغلاق البرنامج بنجاح")
        except Exception as e:
            logging.error(f"خطأ في تشغيل واجهة المستخدم: {e}")
            print(f"❌ خطأ في تشغيل واجهة المستخدم: {e}")
            raise

    except KeyboardInterrupt:
        logging.info("تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        logging.error(f"خطأ غير متوقع: {str(e)}", exc_info=True)
        print(f"❌ حدث خطأ غير متوقع: {str(e)}")
        print("📋 تم حفظ تفاصيل الخطأ في ملف السجل")
        # تجنب استخدام input() في الملف التنفيذي
        import time
        print("سيتم إغلاق البرنامج خلال 5 ثوان...")
        time.sleep(5)

if __name__ == "__main__":
    # عرض معلومات البرنامج
    print("=" * 60)
    print("🏦 برنامج إدارة الأموال الشخصية")
    print("=" * 60)
    print("📋 المميزات:")
    print("   • إدارة المستخدمين والصلاحيات")
    print("   • دعم العملات المتعددة")
    print("   • تتبع الواردات والمصروفات")
    print("   • إدارة الحسابات المالية")
    print("   • التحويلات بين الحسابات")
    print("   • التقارير والإحصائيات")
    print("   • النسخ الاحتياطي التلقائي")
    print("   • واجهة مستخدم أنيقة")
    print("=" * 60)
    print("🚀 بدء التشغيل...")
    print()

    # تشغيل البرنامج
    main()
