
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

excluded module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), gevent.subprocess (optional)
excluded module named pwd - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._distutils.util (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), psutil (optional), gevent.subprocess (optional)
missing module named 'asyncio.coroutines' - imported by typing_extensions (delayed, conditional)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), typing_extensions (top-level), setuptools (top-level), setuptools._distutils.filelist (top-level), setuptools._distutils.util (top-level), setuptools._vendor.jaraco.functools (top-level), setuptools._vendor.more_itertools.more (top-level), setuptools._vendor.more_itertools.recipes (top-level), setuptools._distutils._modified (top-level), setuptools._distutils.compat (top-level), setuptools._distutils.spawn (top-level), setuptools._distutils.compilers.C.base (top-level), setuptools._distutils.fancy_getopt (top-level), setuptools._reqs (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools._distutils.command.bdist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), configparser (top-level), setuptools._distutils.extension (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), pkg_resources (top-level), platformdirs.api (conditional), platformdirs.windows (conditional), platformdirs.unix (conditional), setuptools.command.egg_info (top-level), setuptools._distutils.command.build (top-level), setuptools._distutils.command.sdist (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), setuptools._vendor.wheel.cli.convert (top-level), setuptools._vendor.wheel.cli.tags (top-level), mysql.opentelemetry.attributes (top-level), mysql.opentelemetry.sdk.util (top-level), PIL.Image (top-level), PIL._typing (top-level), numpy.lib._npyio_impl (top-level), numpy.lib._function_base_impl (top-level), numpy._typing._nested_sequence (conditional), numpy._typing._shape (top-level), numpy._typing._dtype_like (top-level), numpy._typing._array_like (top-level), yaml.constructor (top-level), numpy.random.bit_generator (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), PIL.ImageFilter (top-level), PIL.ImagePalette (top-level), pandas._typing (top-level), pytz.lazy (optional), pandas.util._exceptions (conditional), pandas._config.config (conditional), pandas.util.version (top-level), pandas.core.dtypes.inference (conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.util._decorators (conditional), pandas.util._validators (top-level), pandas.core.construction (top-level), pandas.core.common (top-level), pandas.core.frame (top-level), pandas.core.dtypes.concat (conditional), pandas.core.sorting (conditional), pandas.core.indexes.category (conditional), pandas.core.arrays.masked (conditional), pandas.core.apply (conditional), pandas.core.base (conditional), pandas.core.indexing (conditional), pandas.core.strings.base (conditional), pandas.core.strings.object_array (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.internals.blocks (conditional), pandas.core.arrays.timedeltas (conditional), pandas.io.formats.format (top-level), pandas.core.indexes.range (top-level), pandas.core.tools.timedeltas (conditional), pandas.core.indexes.datetimelike (conditional), pandas.core.reshape.concat (conditional), pandas.io.common (top-level), pandas.io.formats.printing (top-level), IPython.external.pickleshare (optional), IPython.core.ultratb (top-level), IPython.core.doctb (top-level), parso.python.tree (optional), sqlite3.dbapi2 (top-level), attr._compat (top-level), attr._make (top-level), referencing._core (top-level), referencing.typing (top-level), referencing.jsonschema (top-level), jsonschema._utils (top-level), jsonschema.exceptions (conditional), jsonschema._types (conditional), jsonpointer (top-level), uri_template.expansions (conditional), uri_template.uritemplate (conditional), jsonschema.validators (top-level), jsonschema._typing (top-level), jsonschema.protocols (conditional), requests.compat (top-level), cryptography.utils (top-level), cryptography.x509.name (top-level), cryptography.x509.base (top-level), cryptography.hazmat.bindings.openssl.binding (top-level), cryptography.x509.extensions (top-level), nbformat.notebooknode (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), matplotlib (top-level), matplotlib.cbook (top-level), matplotlib._path (top-level), matplotlib.colors (top-level), pyparsing.core (top-level), pyparsing.results (top-level), markupsafe (top-level), cycler (top-level), matplotlib.cm (top-level), matplotlib.markers (top-level), matplotlib._mathtext (conditional), matplotlib.axes._base (top-level), matplotlib.spines (top-level), tornado.gen (top-level), tornado.httputil (top-level), matplotlib.pyplot (conditional), matplotlib.typing (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.compilers.C.msvc (top-level), jupyter_client.jsonutil (top-level), pandas.core.indexes.multi (top-level), pandas.io.formats.html (conditional), pandas.io.formats.string (conditional), pandas.io.formats.csvs (top-level), pandas.io.formats.style_render (top-level), pandas.core.interchange.dataframe_protocol (conditional), pandas.core.window.rolling (conditional), pandas.core.series (top-level), pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (conditional), pandas.core.methods.selectn (top-level), pandas.core.strings.accessor (conditional), pandas.core.tools.datetimes (conditional), pandas.io.formats.info (conditional), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.core.groupby.grouper (conditional), pandas.core.groupby.ops (conditional), pandas.io.json._normalize (conditional), pandas.io.parsers.base_parser (conditional), pandas.io.parsers.c_parser_wrapper (conditional), pandas.io.parsers.python_parser (top-level), pandas.io.parsers.readers (conditional), pandas.io.json._json (conditional), pandas.io.stata (conditional), pandas.io.formats.style (conditional), pandas.io.formats.excel (top-level), pandas.io.formats.css (conditional), pandas.io.excel._base (top-level), pandas.io.excel._util (top-level), pandas.core.arrays.interval (conditional), pandas.core.indexes.interval (conditional), pandas.core.arrays.period (conditional), pandas.core.indexes.period (conditional), pandas.core.internals.managers (top-level), pandas.core.internals.ops (conditional), pandas.core.internals.array_manager (conditional), pandas.core.internals.construction (conditional), pandas.core.methods.describe (conditional), pandas.core.generic (conditional), pandas.core.computation.parsing (conditional), pandas.compat.pickle_compat (conditional), pandas.core.computation.ops (conditional), pandas.core.computation.align (conditional), pandas.io.pytables (conditional), pandas.io.sql (conditional), pandas.core.groupby.groupby (top-level), pandas.core.groupby.base (conditional), pandas.core.groupby.indexing (top-level), pandas.core.resample (conditional), pandas.core.groupby.generic (conditional), pandas.core.reshape.merge (top-level), pandas.core.arrays.numeric (conditional), pandas.core.arrays.datetimelike (conditional), pandas.core.arrays.datetimes (conditional), pandas.core.indexes.datetimes (conditional), pandas.core.arrays._mixins (conditional), pandas.core.arrays.categorical (conditional), pandas.core.reshape.melt (conditional), pandas.core.interchange.dataframe (conditional), pandas.io.feather_format (conditional), pandas.io.xml (conditional), pandas.core.reshape.pivot (top-level), pandas.core.arrays.base (conditional), pandas.core.internals.concat (conditional), pandas.core.indexes.base (conditional), pandas.core.dtypes.cast (conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (conditional), pandas.core.util.hashing (conditional), pandas.core.reshape.encoding (top-level), pandas._config.localization (conditional), pandas._testing.contexts (conditional), pandas._testing._warnings (conditional), pandas.io.html (conditional), lxml.html (top-level), lxml.html._setmixin (optional), pandas.io.sas.sasreader (conditional), pandas.io.spss (conditional), schedule (top-level), babel.core (top-level), babel.localedata (top-level), babel.plural (top-level), babel.dates (top-level), babel.support (top-level), babel.util (top-level), babel.messages.catalog (top-level), babel.messages.checkers (top-level), babel.messages.pofile (top-level), babel.messages.extract (top-level), babel.messages.jslexer (top-level), babel.lists (top-level), gevent.contextvars (optional), gevent.tests.test__local (optional), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.groupby (conditional), pandas.plotting._matplotlib.boxplot (conditional), PIL.ImageDraw (top-level)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named ntpath.relpath - imported by ntpath (top-level), babel.messages.extract (top-level)
missing module named ntpath.samefile - imported by ntpath (top-level), cffi.ffiplatform (optional)
missing module named ntpath.join - imported by ntpath (top-level), IPython.lib.display (top-level), zmq (delayed)
missing module named ntpath.abspath - imported by ntpath (top-level), IPython.lib.display (top-level), zmq (delayed)
missing module named ntpath.splitext - imported by ntpath (top-level), IPython.core.display (top-level), IPython.lib.display (top-level)
missing module named ntpath.commonprefix - imported by ntpath (top-level), pygments.regexopt (top-level)
missing module named ntpath.split - imported by ntpath (top-level), pkg_resources (top-level)
missing module named ntpath.isfile - imported by ntpath (top-level), setuptools._distutils.file_util (delayed), IPython.lib.display (top-level)
missing module named ntpath.isdir - imported by ntpath (top-level), setuptools._distutils.file_util (delayed), pkg_resources (top-level), IPython.lib.display (top-level)
missing module named ntpath.exists - imported by ntpath (top-level), setuptools._distutils.file_util (delayed), IPython.lib.display (top-level), zmq (delayed)
missing module named ntpath.dirname - imported by ntpath (top-level), setuptools._distutils.file_util (delayed), zmq (delayed)
missing module named ntpath.basename - imported by ntpath (top-level), setuptools._distutils.file_util (delayed), pygments.lexers (top-level), pygments.formatters (top-level)
missing module named ntpath.realpath - imported by ntpath (top-level), sysconfig (top-level)
missing module named ntpath.devnull - imported by ntpath (top-level), os (top-level)
missing module named ntpath.altsep - imported by ntpath (top-level), os (top-level)
missing module named ntpath.extsep - imported by ntpath (top-level), os (top-level)
missing module named ntpath.defpath - imported by ntpath (top-level), os (top-level)
missing module named ntpath.pathsep - imported by ntpath (top-level), os (top-level)
missing module named ntpath.sep - imported by ntpath (top-level), os (top-level)
missing module named ntpath.pardir - imported by ntpath (top-level), os (top-level), zmq (delayed)
missing module named ntpath.curdir - imported by ntpath (top-level), os (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named posix - imported by shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional), _pyrepl.unix_console (delayed, optional)
excluded module named winreg - imported by importlib._bootstrap_external (conditional), platform (delayed, optional), mimetypes (optional), urllib.request (delayed, conditional, optional), platformdirs.windows (delayed, optional), darkdetect._windows_detect (top-level), IPython.utils.path (delayed, conditional, optional), jedi.api.environment (delayed), winreg (top-level), requests.utils (delayed, conditional, optional), matplotlib.font_manager (delayed), matplotlib (delayed, conditional), setuptools._distutils.compilers.C.msvc (top-level), babel.localtime._win32 (optional), pygments.formatters.img (optional), setuptools.msvc (conditional)
excluded module named nt - imported by shutil (conditional), importlib._bootstrap_external (conditional), _colorize (delayed, conditional, optional), os (delayed, conditional, optional), ctypes (delayed, conditional), _pyrepl.windows_console (delayed, optional)
missing module named _typeshed - imported by setuptools._distutils.dist (conditional), pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), numpy.random.bit_generator (top-level), prompt_toolkit.eventloop.inputhook (conditional), babel.messages.mofile (conditional), babel.messages.pofile (conditional), babel.messages.extract (conditional)
missing module named 'unittest.mock' - imported by setuptools._distutils.compilers.C.msvc (top-level), gevent.tests.test__greenlet (top-level), gevent.tests.test__threading_2 (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
excluded module named difflib - imported by numpy.testing._private.utils (delayed), traitlets.config.configurable (delayed, conditional), parso.python.diff (top-level), jedi.api.refactoring (top-level), babel.messages.catalog (top-level), lxml.html.diff (conditional, optional), zope.interface.ro (delayed, conditional)
excluded module named doctest - imported by numpy.testing._private.utils (delayed), pytz (delayed), tornado.util (delayed), tornado.iostream (delayed), tornado.httputil (delayed), pickletools (delayed), lxml.doctestcompare (top-level), gevent.tests.test__doctests (top-level)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), parso.python.tree (optional), gevent.contextvars (optional), gevent.tests.test__local (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), _pyrepl.curses (optional)
excluded module named termios - imported by getpass (optional), IPython.core.page (delayed, optional), prompt_toolkit.input.vt100 (top-level), _pyrepl.pager (delayed, optional), _pyrepl.unix_console (top-level), _pyrepl.fancy_termios (top-level), _pyrepl.unix_eventqueue (top-level)
excluded module named tty - imported by prompt_toolkit.input.vt100 (top-level), _pyrepl.pager (delayed, optional)
excluded module named fcntl - imported by subprocess (optional), xmlrpc.server (optional), _pyrepl.unix_console (top-level), gevent.fileobject (optional), gevent.os (optional), gevent.subprocess (conditional)
excluded module named msvcrt - imported by subprocess (optional), getpass (optional), IPython.core.page (conditional), prompt_toolkit.input.win32 (conditional), colorama.winterm (optional), _pyrepl.windows_console (top-level), gevent.subprocess (conditional)
missing module named readline - imported by cmd (delayed, conditional, optional), pstats (conditional, optional), site (delayed, optional), rlcompleter (optional), code (delayed, conditional, optional), sqlite3.__main__ (delayed, conditional, optional)
excluded module named pydoc - imported by numpy.lib._utils_impl (delayed), xmlrpc.server (top-level), IPython.core.tbtools (top-level), jedi.api.keywords (top-level), _sitebuiltins (delayed)
excluded module named concurrent.futures - imported by mysql.opentelemetry.sdk.trace (top-level), mysql.opentelemetry.sdk.resources (top-level), numpy.testing._private.utils (top-level), IPython.terminal.debugger (top-level), tornado.concurrent (top-level), tornado.ioloop (top-level), tornado.platform.asyncio (top-level), tornado.gen (top-level), tornado.netutil (top-level), setuptools._distutils.command.build_ext (delayed, optional), jupyter_client.manager (top-level), ipykernel.kernelbase (top-level), gevent.threadpool (optional), gevent.tests.test__contextvars (top-level), gevent.tests.test__threadpool (delayed)
missing module named zipp.Path - imported by setuptools._vendor.zipp (top-level), importlib_resources._compat (conditional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named 'xml.parsers.expat' - imported by plistlib (top-level), defusedxml.common (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
excluded module named nturl2path - imported by urllib.request (conditional)
missing module named 'http.cookiejar' - imported by urllib.request (delayed)
missing module named 'http.client' - imported by urllib.request (top-level), xmlrpc.client (top-level), logging.handlers (delayed), urllib3.exceptions (top-level), urllib3.connection (top-level), urllib3.response (top-level), urllib3.util.response (top-level), urllib3.contrib.emscripten.connection (top-level), urllib3.contrib.emscripten.response (top-level), tornado.httputil (top-level)
missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named _posixsubprocess - imported by subprocess (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named sqlite3.OperationalError - imported by sqlite3 (optional), IPython.core.history (optional)
missing module named sqlite3.DatabaseError - imported by sqlite3 (optional), IPython.core.history (optional)
missing module named deprecated - imported by mysql.opentelemetry.trace (top-level), mysql.opentelemetry.sdk.trace (top-level), mysql.opentelemetry.sdk.util (top-level), mysql.opentelemetry.sdk.util.instrumentation (top-level)
missing module named 'opentelemetry.semconv' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'opentelemetry.sdk' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named opentelemetry - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.context_propagation (conditional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named _mysql_connector - imported by mysql.connector.cursor_cext (top-level), mysql.connector.connection_cext (optional)
missing module named 'opentelemetry.trace' - imported by mysql.connector.opentelemetry.context_propagation (conditional)
missing module named 'dns.resolver' - imported by mysql.connector.pooling (optional)
missing module named dns - imported by mysql.connector.pooling (optional), gevent.tests.monkey_package.issue1526_no_monkey (top-level), gevent.tests.monkey_package.issue1526_with_monkey (top-level)
excluded module named inspect - imported by typing (delayed), ast (delayed, conditional), dataclasses (top-level), importlib.resources._common (top-level), importlib.metadata (top-level), warnings (delayed, conditional), mysql.connector.abstracts (top-level), typing_extensions (top-level), mysql.opentelemetry.importlib_metadata (top-level), setuptools.logging (top-level), setuptools.monkey (top-level), setuptools._vendor.jaraco.functools (top-level), importlib_resources._common (top-level), setuptools.warnings (top-level), setuptools.discovery (delayed, conditional), setuptools._vendor.importlib_metadata (top-level), setuptools.config._apply_pyprojecttoml (top-level), setuptools.config._validate_pyproject.extra_validations (top-level), pkg_resources (top-level), pkgutil (delayed, optional), numpy.lib._utils_impl (delayed), numpy.ma.core (top-level), numpy.testing._private.utils (delayed), psutil._common (delayed, conditional), xmlrpc.server (top-level), pandas.util._exceptions (top-level), pandas.util._decorators (top-level), pandas.core.common (top-level), pandas.core.frame (top-level), pandas.core.dtypes.astype (top-level), pandas.core.apply (top-level), pandas.core.internals.blocks (top-level), bdb (top-level), IPython.core.interactiveshell (top-level), traitlets.traitlets (top-level), traitlets.utils.descriptions (top-level), traitlets.utils.getargspec (top-level), traitlets.utils.warnings (top-level), traitlets.utils.decorators (top-level), IPython.core.oinspect (top-level), IPython.lib.pretty (top-level), IPython.utils.dir2 (top-level), IPython.core.ultratb (top-level), executing.executing (top-level), stack_data.formatting (top-level), stack_data.serializing (top-level), IPython.core.doctb (top-level), IPython.core.tbtools (top-level), IPython.core.debugger (top-level), IPython.terminal.interactiveshell (top-level), IPython.core.async_helpers (top-level), prompt_toolkit.key_binding.key_bindings (top-level), IPython.core.completer (top-level), IPython.core.guarded_eval (top-level), jedi.parser_utils (top-level), jedi.inference.compiled.value (top-level), jedi.inference.names (top-level), jedi.inference.gradual.annotation (top-level), jedi.inference.signature (top-level), jedi.inference.star_args (top-level), jedi.inference.compiled.access (top-level), jedi.inference.param (top-level), jedi.inference.compiled.mixed (top-level), jedi.inference.compiled.subprocess.functions (top-level), jedi.api.helpers (top-level), jedi.api.completion (top-level), jedi.plugins.stdlib (top-level), jedi.plugins.django (top-level), cmd (top-level), decorator (top-level), attr._compat (top-level), attr._make (top-level), IPython.core.magics.code (top-level), IPython.extensions.storemagic (top-level), rlcompleter (top-level), IPython.core.completerlib (top-level), matplotlib (top-level), matplotlib._api.deprecation (top-level), matplotlib.artist (top-level), matplotlib.backend_bases (top-level), matplotlib.colors (top-level), matplotlib.scale (top-level), matplotlib._docstring (top-level), pyparsing.util (top-level), pyparsing.exceptions (delayed), jinja2.nodes (top-level), jinja2.async_utils (top-level), jinja2.filters (top-level), pyparsing.diagram (top-level), matplotlib.pyplot (top-level), matplotlib.patches (top-level), matplotlib.figure (top-level), matplotlib.axes._base (top-level), mpl_toolkits.mplot3d.axis3d (top-level), tornado.web (top-level), tornado.util (top-level), tornado.ioloop (top-level), tornado.gen (top-level), pycparser.ply.yacc (top-level), pycparser.ply.lex (top-level), jupyter_core.utils (top-level), jupyter_client.client (top-level), ipykernel.kernelbase (top-level), debugpy.common.log (top-level), debugpy.common.util (top-level), pandas.core.window.rolling (top-level), openpyxl.compat (top-level), openpyxl.worksheet.worksheet (top-level), openpyxl.worksheet._write_only (top-level), pandas.core.computation.scope (top-level), qtpy.QtDataVisualization (conditional), pandas.core.groupby.groupby (top-level), pandas.core.groupby.numba_ (top-level), pandas._testing._warnings (top-level), C:\Users\<USER>\Desktop\money manager\main.py (top-level), lxml.html.diff (optional), zope.interface.exceptions (delayed), gevent.monkey._main (delayed), zope.interface.verify (top-level), gevent.testing.testcase (delayed), gevent.tests.test__util (delayed)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named StringIO - imported by six (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
excluded module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
excluded module named html - imported by xmlrpc.server (top-level), IPython.core.oinspect (top-level), IPython.core.display (top-level), IPython.lib.display (top-level), stack_data.core (top-level), stack_data.serializing (top-level), markupsafe (delayed), tornado.escape (top-level), lxml.doctestcompare (optional), lxml.html.diff (conditional)
missing module named 'http.server' - imported by xmlrpc.server (top-level), gevent.tests.test__greenness (optional)
excluded module named xml.parsers - imported by xmlrpc.client (top-level)
excluded module named xml.sax - imported by defusedxml.sax (top-level)
missing module named 'xml.dom.minidom' - imported by defusedxml.minidom (top-level), prompt_toolkit.formatted_text.html (top-level), pandas.io.formats.xml (delayed)
missing module named 'xml.dom.pulldom' - imported by defusedxml.pulldom (top-level)
missing module named 'xml.sax.expatreader' - imported by defusedxml.expatreader (top-level)
missing module named 'xml.dom.expatbuilder' - imported by defusedxml.expatbuilder (top-level)
missing module named 'xml.etree.cElementTree' - imported by defusedxml.cElementTree (top-level)
missing module named 'xml.etree.ElementTree' - imported by defusedxml.cElementTree (top-level), defusedxml.ElementTree (top-level), PIL.Image (conditional), openpyxl.xml.functions (top-level), et_xmlfile.xmlfile (top-level), et_xmlfile.incremental_tree (top-level), pandas.io.xml (delayed, conditional), pandas.io.formats.xml (delayed)
missing module named 'xml.sax.xmlreader' - imported by lxml.sax (delayed)
missing module named 'xml.sax.handler' - imported by lxml.sax (top-level)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named 'html.entities' - imported by pyparsing.helpers (top-level), bs4.dammit (top-level), lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named 'html.parser' - imported by bs4.builder._htmlparser (top-level)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (top-level), lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level), lxml.html.html5parser (top-level)
excluded module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
excluded module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional), gevent.tests.test__example_wsgiserver (optional), gevent.tests.test__greenness (optional)
missing module named 'cython.cimports' - imported by lxml.html.diff (optional), zmq.backend.cython._zmq (top-level)
missing module named cython - imported by lxml.html.diff (optional), lxml.html._difflib (optional), zmq.backend.cython._zmq (top-level)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named cgi - imported by lxml.doctestcompare (optional)
missing module named cssselect - imported by lxml.cssselect (optional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (optional), matplotlib.cbook (optional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), cffi.lock (conditional, optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named 'unittest.case' - imported by numpy.testing._private.utils (top-level)
excluded module named unittest - imported by numpy.testing (top-level), numpy.testing._private.utils (top-level), tornado.util (conditional), tornado.httputil (conditional), gevent.testing (top-level), gevent.testing.flaky (top-level), gevent.testing.leakcheck (top-level), gevent.testing.openfiles (top-level), gevent.testing.resources (delayed, conditional), gevent.testing.skipping (top-level), gevent.testing.testcase (top-level), gevent.testing.util (top-level), gevent.tests.lock_tests (top-level), gevent.tests.test___config (top-level), gevent.tests.test___monitor (top-level), gevent.tests.test__all__ (top-level), gevent.tests.test__ares_timeout (top-level), gevent.tests.test__close_backend_fd (top-level), gevent.tests.test__compat (top-level), gevent.tests.test__contextvars (top-level), gevent.tests.test__core (top-level), gevent.tests.test__core_fork (top-level), gevent.tests.test__destroy (top-level), gevent.tests.test__destroy_default_loop (top-level), gevent.tests.test__doctests (top-level), gevent.tests.test__events (top-level), gevent.tests.test__example_webproxy (top-level), gevent.tests.test__examples (top-level), gevent.tests.test__execmodules (top-level), gevent.tests.test__fileobject (top-level), gevent.tests.test__greenlet (top-level), gevent.tests.test__hub (top-level), gevent.tests.test__hub_join (top-level), gevent.tests.test__hub_join_timeout (top-level), gevent.tests.test__import_blocking_in_greenlet (conditional), gevent.tests.test__issue112 (top-level), gevent.tests.test__issue1686 (top-level), gevent.tests.test__issue1864 (top-level), gevent.tests.test__issues461_471 (conditional), gevent.tests.test__memleak (top-level), gevent.tests.test__monkey (top-level), gevent.tests.test__monkey_queue (top-level), gevent.tests.test__monkey_ssl_warning (top-level), gevent.tests.test__monkey_ssl_warning2 (top-level), gevent.tests.test__monkey_ssl_warning3 (top-level), gevent.tests.test__pool (top-level), gevent.tests.test__pywsgi (top-level), gevent.tests.test__queue (top-level), gevent.tests.test__resolver_dnspython (top-level), gevent.tests.test__select (top-level), gevent.tests.test__server (top-level), gevent.tests.test__socket (top-level), gevent.tests.test__socket_dns (top-level), gevent.tests.test__socket_dns6 (top-level), gevent.tests.test__socket_send_memoryview (top-level), gevent.tests.test__socketpair (top-level), gevent.tests.test__subprocess (top-level), gevent.tests.test__threading_2 (top-level), gevent.tests.test__threading_fork_from_dummy (top-level), gevent.tests.test__threading_vs_settrace (top-level), gevent.tests.test__util (top-level)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named pyarrow - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.interchange.utils (delayed, conditional), pandas.core.strings.accessor (delayed, conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional), pandas.plotting._matplotlib.misc (delayed, conditional), pandas.plotting._matplotlib.hist (delayed)
missing module named scipy - imported by pandas.core.dtypes.common (delayed, conditional, optional), pandas.core.missing (delayed)
missing module named pygments.formatters.LatexFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed), IPython.core.oinspect (top-level), stack_data.core (delayed)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
excluded module named _winreg - imported by pygments.formatters.img (optional)
excluded module named PIL.TiffImagePlugin - imported by PIL (delayed), PIL.ImageFile (delayed), PIL.Image (top-level), PIL.MpoImagePlugin (top-level)
excluded module named PIL.ImageDraw2 - imported by PIL (conditional), PIL.ImageDraw (delayed, conditional)
excluded module named colorsys - imported by PIL.ImageColor (delayed, conditional), prompt_toolkit.styles.style_transformation (top-level)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (top-level), IPython.core.oinspect (top-level)
excluded module named wave - imported by IPython.lib.display (delayed)
excluded module named xml.dom - imported by IPython.core.display (delayed)
missing module named 'yapf.yapflib' - imported by IPython.terminal.interactiveshell (delayed)
missing module named yapf - imported by IPython.terminal.interactiveshell (delayed)
missing module named black - imported by IPython.terminal.interactiveshell (delayed)
missing module named jupyter_ai - imported by IPython.terminal.shortcuts.auto_suggest (delayed, optional)
missing module named jupyter_ai_magics - imported by IPython.terminal.shortcuts.auto_suggest (delayed, optional)
missing module named prompt_toolkit.filters.vi_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.document (top-level), prompt_toolkit.key_binding.bindings.page_navigation (top-level), prompt_toolkit.widgets.toolbars (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named 'prompt_toolkit.key_binding.key_bindings.vi' - imported by prompt_toolkit.key_binding.vi_state (conditional)
missing module named 'backports.functools_lru_cache' - imported by wcwidth.wcwidth (optional)
missing module named prompt_toolkit.filters.is_done - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.base (top-level), prompt_toolkit.shortcuts.progress_bar.base (top-level), prompt_toolkit.shortcuts.prompt (top-level)
missing module named prompt_toolkit.filters.has_completions - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.toolbars (top-level), prompt_toolkit.widgets.dialogs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.vi_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.emacs_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), prompt_toolkit.key_binding.bindings.emacs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.is_searching - imported by prompt_toolkit.filters (top-level), prompt_toolkit.search (top-level), prompt_toolkit.key_binding.bindings.search (top-level), prompt_toolkit.key_binding.bindings.vi (top-level)
excluded module named asyncio - imported by IPython.core.async_helpers (top-level), prompt_toolkit.application.application (top-level), prompt_toolkit.buffer (top-level), prompt_toolkit.key_binding.key_processor (top-level), prompt_toolkit.patch_stdout (top-level), prompt_toolkit.application.run_in_terminal (top-level), prompt_toolkit.eventloop.async_generator (top-level), prompt_toolkit.eventloop.utils (top-level), prompt_toolkit.eventloop.inputhook (top-level), prompt_toolkit.input.win32 (top-level), prompt_toolkit.input.vt100 (top-level), prompt_toolkit.history (top-level), prompt_toolkit.key_binding.bindings.completion (top-level), prompt_toolkit.shortcuts.dialogs (top-level), prompt_toolkit.shortcuts.prompt (top-level), prompt_toolkit.renderer (top-level), IPython.terminal.debugger (top-level), IPython.terminal.shortcuts.auto_suggest (top-level), IPython.core.magics.script (top-level), jinja2.environment (delayed, conditional), tornado.concurrent (top-level), tornado.util (top-level), tornado.ioloop (top-level), tornado.platform.asyncio (top-level), tornado.gen (top-level), tornado.process (top-level), tornado.iostream (top-level), tornado.netutil (top-level), tornado.http1connection (top-level), tornado.httputil (conditional), tornado.websocket (top-level), matplotlib.backends.backend_webagg_core (top-level), matplotlib.backends.backend_webagg (delayed), zmq.asyncio (top-level), zmq._future (top-level), jupyter_client.channels (top-level), jupyter_core.utils (top-level), zmq.eventloop.zmqstream (top-level), jupyter_client.client (top-level), jupyter_client.manager (top-level), jupyter_client.provisioning.local_provisioner (top-level), jupyter_client.multikernelmanager (top-level), ipykernel.iostream (top-level), ipykernel.ipkernel (top-level), ipykernel.kernelbase (top-level), ipykernel.eventloops (delayed, conditional), nest_asyncio (top-level), ipykernel.kernelapp (delayed, conditional, optional)
missing module named numpydoc - imported by jedi.inference.docstrings (delayed)
missing module named 'IPython.config' - imported by IPython.core.history (conditional)
missing module named sip - imported by IPython.external.qt_loaders (delayed, optional), matplotlib.backends.qt_compat (delayed, conditional)
missing module named 'asyncio.exceptions' - imported by IPython.core.magics.script (top-level)
excluded module named resource - imported by IPython.utils.timing (optional)
missing module named rpds.HashTrieMap - imported by rpds (top-level), referencing._core (top-level), jsonschema._types (top-level), jsonschema.validators (top-level)
missing module named rpds.List - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieSet - imported by rpds (top-level), referencing._core (top-level)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named 'http.cookies' - imported by requests.compat (top-level), tornado.web (top-level), tornado.httputil (top-level)
excluded module named http - imported by requests.compat (top-level), gevent.pywsgi (optional), gevent.tests.test__socket_ssl (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named rfc3987 - imported by jsonschema._format (optional)
missing module named cached_property - imported by fqdn._compat (conditional)
missing module named traitlets.config.Application - imported by traitlets.config (delayed, conditional), traitlets.log (delayed, conditional), ipykernel.kernelspec (top-level)
missing module named argcomplete - imported by traitlets.config.loader (delayed, optional), traitlets.config.argcomplete_config (optional)
missing module named 'multiprocessing.queues' - imported by logging.config (delayed)
missing module named 'asyncio.events' - imported by prompt_toolkit.shortcuts.utils (top-level), nest_asyncio (top-level)
missing module named prompt_toolkit.filters.vi_insert_multiple_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.processors (top-level)
missing module named pexpect - imported by IPython.utils._process_posix (delayed, conditional), jupyter_client.ssh.tunnel (optional)
missing module named System - imported by IPython.utils._process_cli (top-level)
missing module named clr - imported by IPython.utils._process_cli (top-level)
missing module named trio - imported by IPython.core.async_helpers (delayed), ipykernel.trio_runner (top-level)
missing module named curio - imported by IPython.core.async_helpers (delayed)
excluded module named pdb - imported by IPython.core.debugger (top-level), prompt_toolkit.application.application (delayed), IPython.core.magics.execution (top-level), IPython.terminal.debugger (conditional), ipykernel.kernelapp (delayed)
missing module named pytest - imported by executing._pytest_utils (delayed, optional), pandas._testing._io (delayed), pandas._testing (delayed)
missing module named 'astroid.node_classes' - imported by asttokens.astroid_compat (optional)
missing module named 'astroid.nodes' - imported by asttokens.astroid_compat (optional)
missing module named astroid - imported by asttokens.astroid_compat (optional), asttokens.util (optional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
excluded module named multiprocessing - imported by tornado.process (top-level), jupyter_client.ssh.tunnel (top-level), gevent.subprocess (delayed, conditional), gevent.testing.testrunner (top-level), gevent.tests.test__core_fork (top-level), gevent.tests.test__issue230 (top-level), gevent.tests.test__issue600 (top-level)
excluded module named PIL.PngImagePlugin - imported by PIL (delayed, optional), PIL.Image (top-level), matplotlib.colors (top-level), matplotlib.image (top-level), PIL.IcnsImagePlugin (top-level)
missing module named gi - imported by matplotlib.cbook (delayed, conditional), ipykernel.gui.gtk3embed (top-level)
excluded module named matplotlib.backends._backend_agg - imported by matplotlib.backends.backend_agg (top-level)
missing module named shiboken2 - imported by matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named shiboken6 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named docrepr - imported by IPython.core.interactiveshell (optional)
missing module named cPickle - imported by IPython.external.pickleshare (optional), pycparser.ply.yacc (delayed, optional)
missing module named pathlib2 - imported by IPython.external.pickleshare (optional)
missing module named netifaces - imported by jupyter_client.localinterfaces (delayed)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional), gevent._compat (optional)
missing module named zope.schema - imported by zope (optional), gevent._interfaces (optional)
missing module named _continuation - imported by gevent.greenlet (conditional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional), gevent.tests.lock_tests (optional), gevent.tests.test__core_async (optional), gevent.tests.test__refcount (optional), gevent.tests.test__thread (optional)
missing module named httplib - imported by gevent.tests.test__socket_ssl (optional)
missing module named selectors2 - imported by gevent.selectors (optional), gevent.tests.test__monkey_selectors (optional)
missing module named _import_wait - imported by gevent.tests.test__import_wait (optional)
missing module named _blocks_at_top_level - imported by gevent.tests.test__import_blocking_in_greenlet (delayed, optional)
excluded module named SimpleHTTPServer - imported by gevent.tests.test__greenness (optional)
excluded module named BaseHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named getaddrinfo_module - imported by gevent.tests.test__getaddrinfo_import (optional)
missing module named gevent.resolver.cares - imported by gevent.resolver.ares (top-level), gevent.ares (top-level), gevent.tests.test__ares_host_result (optional)
missing module named 'dns.rdtypes' - imported by gevent.tests.monkey_package.issue1526_no_monkey (top-level)
missing module named 'multiprocessing.pool' - imported by gevent.testing.testrunner (top-level)
excluded module named test - imported by gevent.testing.monkey_test (optional), gevent.testing.support (delayed, conditional, optional)
missing module named __builtin__ - imported by gevent.backdoor (delayed, optional), gevent.libev.corecffi (conditional), gevent.testing.six (conditional)
missing module named 'test.libregrtest' - imported by gevent.testing.resources (delayed, optional)
missing module named 'test.lock_tests' - imported by gevent.testing.monkey_test (optional)
missing module named 'test.support' - imported by gevent.testing.monkey_test (optional)
missing module named objgraph - imported by gevent.testing.leakcheck (optional)
missing module named mock - imported by gevent.testing (optional)
missing module named mimetools - imported by gevent.pywsgi (optional)
missing module named _setuputils - imported by gevent.libev._corecffi_build (optional), gevent.libuv._corecffi_build (optional)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named gevent.libev._corecffi - imported by gevent.libev (top-level), gevent.libev.corecffi (top-level), gevent.libev.watcher (top-level)
missing module named _setuplibev - imported by gevent.libev._corecffi_build (optional)
missing module named zmq.backend.zmq_version_info - imported by zmq.backend (top-level), zmq.sugar.version (top-level)
missing module named zmq.backend.Frame - imported by zmq.backend (top-level), zmq.sugar.frame (top-level), zmq.sugar.tracker (top-level)
missing module named zmq.backend.Socket - imported by zmq.backend (top-level), zmq.sugar.socket (top-level)
missing module named zmq.backend.zmq_poll - imported by zmq.backend (top-level), zmq.sugar.poll (top-level)
missing module named pyczmq - imported by zmq.sugar.context (delayed)
missing module named zmq.backend.Context - imported by zmq.backend (top-level), zmq.sugar.context (top-level)
missing module named zmq.backend.proxy - imported by zmq.backend (top-level), zmq.sugar (top-level)
missing module named zmq.ZMQError - imported by zmq (delayed, optional), zmq.sugar.attrsettr (delayed, optional)
missing module named zmq.backend.zmq_errno - imported by zmq.backend (delayed), zmq.error (delayed, conditional)
missing module named zmq.backend.strerror - imported by zmq.backend (delayed), zmq.error (delayed)
missing module named zmq.zmq_version_info - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.zmq_version - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named 'asyncio.futures' - imported by jupyter_client.manager (top-level)
missing module named _subprocess - imported by jupyter_client.launcher (delayed, conditional, optional), ipykernel.parentpoller (delayed, optional)
missing module named paramiko - imported by jupyter_client.ssh.tunnel (optional)
missing module named cloudpickle - imported by ipykernel.pickleutil (delayed)
missing module named dill - imported by ipykernel.pickleutil (delayed)
missing module named 'ipyparallel.serialize' - imported by ipykernel.ipkernel (delayed, optional), ipykernel.serialize (optional), ipykernel.pickleutil (optional)
missing module named ipykernel.get_connection_info - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.get_connection_file - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.connect_qtconsole - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named PySide6 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named PySide2 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named PyQt5 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named 'gi.repository' - imported by ipykernel.gui.gtk3embed (top-level)
missing module named gtk - imported by ipykernel.gui.gtkembed (top-level)
missing module named gobject - imported by ipykernel.gui.gtkembed (top-level)
missing module named wx - imported by ipykernel.eventloops (delayed), IPython.lib.guisupport (delayed)
missing module named ipyparallel - imported by ipykernel.zmqshell (delayed, conditional)
missing module named appnope - imported by ipykernel.ipkernel (delayed, conditional)
missing module named '_pydevd_bundle.pydevd_api' - imported by ipykernel.debugger (delayed)
missing module named '_pydevd_bundle.pydevd_suspended_frames' - imported by ipykernel.debugger (optional)
missing module named _pydevd_bundle - imported by debugpy._vendored.force_pydevd (top-level), ipykernel.debugger (optional)
missing module named pydevd_file_utils - imported by debugpy.server.api (top-level)
missing module named '_pydevd_bundle.pydevd_constants' - imported by debugpy.server.api (top-level)
missing module named pydevd - imported by debugpy._vendored.force_pydevd (top-level), debugpy.server.api (top-level)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (delayed, conditional), pandas.core.arrays.sparse.accessor (delayed)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named 'PyQt6.QtDataVisualization' - imported by qtpy.QtDataVisualization (conditional, optional)
missing module named Foundation - imported by darkdetect._mac_detect (optional), pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional)
missing module named fsspec - imported by pandas.io.orc (conditional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named 'google.auth' - imported by pandas.io.gbq (conditional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional)
missing module named reportlab_mods - imported by reportlab (optional)
missing module named 'reportlab.local_rl_mods' - imported by reportlab (optional)
missing module named PyObjCTools - imported by darkdetect._mac_detect (optional)
excluded module named ntpath - imported by pathlib._local (top-level), ntpath (top-level), os (conditional), pkg_resources (top-level), C:\Users\<USER>\Desktop\money manager\main.py (top-level)
excluded module named posixpath - imported by fnmatch (top-level), pathlib._local (top-level), importlib.metadata (top-level), zipfile._path (top-level), os (conditional), mysql.opentelemetry.importlib_metadata (top-level), mimetypes (top-level), setuptools._vendor.importlib_metadata (top-level), pkg_resources (top-level), setuptools.wheel (top-level), setuptools.archive_util (top-level), setuptools._vendor.zipp (top-level), jinja2.loaders (top-level), tornado.template (top-level), openpyxl.packaging.relationship (top-level), C:\Users\<USER>\Desktop\money manager\main.py (top-level)
excluded module named PIL.XpmImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.XbmImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.XVThumbImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.WmfImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.TgaImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.SunImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.SpiderImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.SgiImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.PsdImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.PixarImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.PdfImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.PcxImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.PcdImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.PalmImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.MspImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.MpegImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.MicImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.McIdasImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.Jpeg2KImagePlugin - imported by PIL (conditional), PIL.IcnsImagePlugin (conditional), PIL.Image (top-level)
excluded module named PIL.IptcImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.ImtImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.ImImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.IcoImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.Hdf5StubImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.GbrImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.FpxImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.FliImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.EpsImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.DcxImagePlugin - imported by PIL.Image (top-level)
excluded module named PIL.CurImagePlugin - imported by PIL.Image (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
excluded module named PIL.ImageCms - imported by PIL (delayed, conditional), PIL.Image (delayed, conditional)
excluded module named PIL.PpmImagePlugin - imported by PIL (delayed, optional), PIL.Image (top-level)
excluded module named PIL.JpegImagePlugin - imported by PIL (delayed, optional), PIL.Image (top-level), PIL.BlpImagePlugin (delayed), PIL.MpoImagePlugin (top-level)
excluded module named PIL.GifImagePlugin - imported by PIL (delayed, optional), PIL.Image (top-level)
