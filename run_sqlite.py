#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل برنامج إدارة الأموال مع SQLite
"""

import sys
import os
import logging
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """إعداد نظام تسجيل الأحداث"""
    try:
        logs_dir = "logs"
        os.makedirs(logs_dir, exist_ok=True)
        log_filename = os.path.join(logs_dir, f"money_manager_{datetime.now().strftime('%Y%m%d')}.log")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        print(f"✅ تم إعداد نظام السجلات: {log_filename}")
    except Exception as e:
        print(f"⚠️ تعذر إعداد نظام السجلات: {e}")
        logging.basicConfig(level=logging.INFO)

def check_requirements():
    """فحص المتطلبات الأساسية"""
    try:
        import customtkinter
        print("✅ customtkinter متوفر")
    except ImportError:
        print("❌ customtkinter غير متوفر")
        print("💡 قم بتثبيته: pip install customtkinter")
        return False
    
    try:
        import bcrypt
        print("✅ bcrypt متوفر")
    except ImportError:
        print("❌ bcrypt غير متوفر")
        print("💡 قم بتثبيته: pip install bcrypt")
        return False
    
    try:
        from PIL import Image
        print("✅ Pillow متوفر")
    except ImportError:
        print("❌ Pillow غير متوفر")
        print("💡 قم بتثبيته: pip install Pillow")
        return False
    
    return True

def setup_sqlite_config():
    """إعداد قاعدة البيانات SQLite"""
    try:
        # تعديل إعدادات قاعدة البيانات لاستخدام SQLite
        config_dir = "config"
        os.makedirs(config_dir, exist_ok=True)
        
        # إنشاء ملف إعدادات SQLite
        sqlite_config = {
            "database_type": "sqlite",
            "database_path": "money_manager.db",
            "charset": "utf8mb4"
        }
        
        with open(os.path.join(config_dir, "database_settings.json"), "w", encoding="utf-8") as f:
            import json
            json.dump(sqlite_config, f, ensure_ascii=False, indent=4)
        
        print("✅ تم إعداد قاعدة بيانات SQLite")
        return True
    except Exception as e:
        print(f"❌ خطأ في إعداد SQLite: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏦 برنامج إدارة الأموال الشخصية - إصدار SQLite")
    print("=" * 60)
    
    # إعداد نظام السجلات
    setup_logging()
    
    # فحص المتطلبات
    print("\n🔍 فحص المتطلبات...")
    if not check_requirements():
        print("\n❌ بعض المتطلبات غير متوفرة")
        input("اضغط Enter للخروج...")
        return
    
    # إعداد SQLite
    print("\n🔧 إعداد قاعدة البيانات...")
    if not setup_sqlite_config():
        print("\n❌ فشل في إعداد قاعدة البيانات")
        input("اضغط Enter للخروج...")
        return
    
    try:
        print("\n🚀 بدء تشغيل البرنامج...")
        
        # استيراد وتشغيل واجهة تسجيل الدخول
        from gui.login_window import LoginWindow
        
        app = LoginWindow()
        app.run()
        
        print("✅ تم إغلاق البرنامج بنجاح")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("💡 تأكد من وجود جميع ملفات البرنامج")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        logging.error(f"خطأ في التشغيل: {e}", exc_info=True)
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
